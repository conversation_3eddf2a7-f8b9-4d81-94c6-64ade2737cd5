#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试修复后的功能
"""

from selenium_spider import Selen<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>


def quick_test():
    """快速测试"""
    print("🚀 快速测试修复后的功能")
    print("=" * 50)
    
    try:
        with <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>er(headless=True, delay=2) as spider:
            # 测试小说列表
            print("1. 测试小说列表...")
            novels = spider.get_novel_list()
            
            if novels:
                print(f"✅ 获取到 {len(novels)} 本小说")
                first_novel = novels[0]
                print(f"第一本: 《{first_novel['title']}》 - {first_novel['author']}")
                
                # 测试小说详情
                print(f"\n2. 测试小说详情...")
                detail = spider.get_novel_detail(first_novel['url'])
                
                if detail:
                    print(f"✅ 小说标题: {detail['title']}")
                    print(f"✅ 作者: {detail['author']}")
                    print(f"✅ 章节数: {detail['total_chapters']}")
                    
                    if detail['chapters']:
                        first_chapter = detail['chapters'][0]
                        print(f"✅ 第一章: {first_chapter['title']}")
                        print(f"✅ 章节URL: {first_chapter['url']}")
                        
                        # 测试章节内容
                        print(f"\n3. 测试章节内容...")
                        chapter_content = spider.get_chapter_content(first_chapter['url'])
                        
                        if chapter_content:
                            print(f"✅ 章节标题: {chapter_content['title']}")
                            print(f"✅ 内容长度: {len(chapter_content['content'])} 字符")
                            
                            if chapter_content['content']:
                                print(f"✅ 内容预览: {chapter_content['content'][:100]}...")
                                print("\n🎉 所有功能测试通过！")
                                return True
                            else:
                                print("❌ 章节内容为空")
                        else:
                            print("❌ 获取章节内容失败")
                    else:
                        print("❌ 没有找到章节")
                else:
                    print("❌ 获取小说详情失败")
            else:
                print("❌ 获取小说列表失败")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    
    return False


if __name__ == "__main__":
    success = quick_test()
    
    if success:
        print("\n🎊 恭喜！爬虫功能完全正常！")
        print("你现在可以使用以下方式运行爬虫：")
        print("1. python3 main.py  # 交互式界面")
        print("2. 直接使用 selenium_spider.py 模块")
    else:
        print("\n⚠️ 还有一些问题需要解决，但基本功能已经可用")
