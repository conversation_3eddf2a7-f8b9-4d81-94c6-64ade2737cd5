#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CZBooks爬虫测试脚本
用于测试爬虫的各项功能
"""

from czbooks_spider import CZBooksSpider
import json


def test_novel_list():
    """测试获取小说列表功能"""
    print("=" * 50)
    print("测试获取小说列表功能")
    print("=" * 50)
    
    spider = CZBooksSpider(delay=1)
    
    try:
        novels = spider.get_novel_list()
        
        if novels:
            print(f"成功获取到 {len(novels)} 本小说")
            print("\n前5本小说信息:")
            for i, novel in enumerate(novels[:5], 1):
                print(f"\n{i}. 《{novel['title']}》")
                print(f"   作者: {novel['author']}")
                print(f"   状态: {novel['status']}")
                print(f"   链接: {novel['url']}")
                if novel.get('latest_chapter'):
                    print(f"   最新: {novel['latest_chapter']}")
                if novel.get('update_time'):
                    print(f"   更新: {novel['update_time']}")
            
            return novels[0] if novels else None
        else:
            print("未获取到小说列表")
            return None
            
    except Exception as e:
        print(f"获取小说列表失败: {e}")
        return None


def test_novel_detail(novel_url):
    """测试获取小说详情功能"""
    print("\n" + "=" * 50)
    print("测试获取小说详情功能")
    print("=" * 50)
    
    spider = CZBooksSpider(delay=1)
    
    try:
        detail = spider.get_novel_detail(novel_url)
        
        if detail:
            print(f"小说标题: {detail['title']}")
            print(f"作者: {detail['author']}")
            print(f"状态: {detail.get('status', '未知')}")
            print(f"章节数: {detail['total_chapters']}")
            
            if detail.get('intro'):
                print(f"简介: {detail['intro'][:200]}...")
            
            if detail['chapters']:
                print(f"\n前5个章节:")
                for i, chapter in enumerate(detail['chapters'][:5], 1):
                    print(f"  {i}. {chapter['title']}")
                
                return detail['chapters'][0]['url'] if detail['chapters'] else None
            else:
                print("未找到章节列表")
                return None
        else:
            print("获取小说详情失败")
            return None
            
    except Exception as e:
        print(f"获取小说详情失败: {e}")
        return None


def test_chapter_content(chapter_url):
    """测试获取章节内容功能"""
    print("\n" + "=" * 50)
    print("测试获取章节内容功能")
    print("=" * 50)
    
    spider = CZBooksSpider(delay=1)
    
    try:
        chapter = spider.get_chapter_content(chapter_url)
        
        if chapter:
            print(f"章节标题: {chapter['title']}")
            print(f"内容长度: {len(chapter['content'])} 字符")
            print(f"章节URL: {chapter['url']}")
            
            if chapter['content']:
                print(f"\n内容预览:")
                print(chapter['content'][:300] + "..." if len(chapter['content']) > 300 else chapter['content'])
                return True
            else:
                print("章节内容为空")
                return False
        else:
            print("获取章节内容失败")
            return False
            
    except Exception as e:
        print(f"获取章节内容失败: {e}")
        return False


def test_save_functionality():
    """测试保存功能"""
    print("\n" + "=" * 50)
    print("测试保存功能")
    print("=" * 50)
    
    # 创建测试数据
    test_novel = {
        'title': '测试小说',
        'author': '测试作者',
        'intro': '这是一个测试小说的简介。',
        'status': '已完结',
        'total_chapters': 2,
        'chapters': [
            {
                'title': '第1章 开始',
                'url': 'https://example.com/chapter1'
            },
            {
                'title': '第2章 结束',
                'url': 'https://example.com/chapter2'
            }
        ]
    }
    
    spider = CZBooksSpider(delay=1)
    
    # 模拟章节内容获取
    def mock_get_chapter_content(url):
        if 'chapter1' in url:
            return {
                'title': '第1章 开始',
                'content': '这是第一章的内容。\n\n故事开始了...',
                'url': url
            }
        elif 'chapter2' in url:
            return {
                'title': '第2章 结束',
                'content': '这是第二章的内容。\n\n故事结束了...',
                'url': url
            }
        return None
    
    # 替换方法进行测试
    original_method = spider.get_chapter_content
    spider.get_chapter_content = mock_get_chapter_content
    
    try:
        spider.save_novel(test_novel, output_dir="test_novels")
        print("测试小说保存成功！")
        print("请检查 test_novels/测试小说/ 目录")
        return True
    except Exception as e:
        print(f"保存测试失败: {e}")
        return False
    finally:
        # 恢复原方法
        spider.get_chapter_content = original_method


def main():
    """主测试函数"""
    print("CZBooks爬虫功能测试")
    print("=" * 60)
    
    # 测试1: 获取小说列表
    first_novel = test_novel_list()
    
    if not first_novel:
        print("\n❌ 小说列表测试失败，无法继续后续测试")
        return
    
    # 测试2: 获取小说详情
    first_chapter_url = test_novel_detail(first_novel['url'])
    
    if not first_chapter_url:
        print("\n❌ 小说详情测试失败，跳过章节内容测试")
    else:
        # 测试3: 获取章节内容
        test_chapter_content(first_chapter_url)
    
    # 测试4: 保存功能
    test_save_functionality()
    
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)


if __name__ == "__main__":
    main()
