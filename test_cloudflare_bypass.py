#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试不同的Cloudflare绕过方法
"""

import time


def test_undetected_chromedriver():
    """测试undetected-chromedriver方法"""
    print("=" * 60)
    print("测试 undetected-chromedriver 方法")
    print("=" * 60)
    
    try:
        from undetected_spider import UndetectedCZBooksSpider
        
        with UndetectedCZBooksSpider(headless=True, delay=1) as spider:
            print("1. 测试访问首页...")
            novels = spider.get_novel_list()
            
            if novels:
                print(f"✅ 成功！获取到 {len(novels)} 本小说")
                print(f"第一本: 《{novels[0]['title']}》 - {novels[0]['author']}")
                return True, "undetected-chromedriver"
            else:
                print("❌ 失败：未获取到小说列表")
                return False, "undetected-chromedriver"
                
    except ImportError:
        print("❌ undetected-chromedriver 未安装")
        print("安装命令: pip install undetected-chromedriver")
        return False, "undetected-chromedriver (未安装)"
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False, "undetected-chromedriver"


def test_cloudscraper():
    """测试cloudscraper方法"""
    print("\n" + "=" * 60)
    print("测试 cloudscraper 方法")
    print("=" * 60)
    
    try:
        from cloudscraper_spider import CloudscraperCZBooksSpider
        
        spider = CloudscraperCZBooksSpider(delay=1)
        
        print("1. 测试访问首页...")
        novels = spider.get_novel_list()
        
        if novels:
            print(f"✅ 成功！获取到 {len(novels)} 本小说")
            print(f"第一本: 《{novels[0]['title']}》 - {novels[0]['author']}")
            return True, "cloudscraper"
        else:
            print("❌ 失败：未获取到小说列表")
            return False, "cloudscraper"
            
    except ImportError:
        print("❌ cloudscraper 未安装")
        print("安装命令: pip install cloudscraper")
        return False, "cloudscraper (未安装)"
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False, "cloudscraper"


def test_regular_selenium():
    """测试普通Selenium方法（对比）"""
    print("\n" + "=" * 60)
    print("测试 普通Selenium 方法（对比）")
    print("=" * 60)
    
    try:
        from selenium_spider import SeleniumCZBooksSpider
        
        with SeleniumCZBooksSpider(headless=True, delay=1) as spider:
            print("1. 测试访问首页...")
            novels = spider.get_novel_list()
            
            if novels:
                print(f"✅ 成功！获取到 {len(novels)} 本小说")
                print(f"第一本: 《{novels[0]['title']}》 - {novels[0]['author']}")
                return True, "普通Selenium"
            else:
                print("❌ 失败：未获取到小说列表")
                return False, "普通Selenium"
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False, "普通Selenium"


def install_dependencies():
    """安装所需依赖"""
    print("=" * 60)
    print("安装Cloudflare绕过工具")
    print("=" * 60)
    
    import subprocess
    import sys
    
    packages = [
        "undetected-chromedriver",
        "cloudscraper"
    ]
    
    for package in packages:
        try:
            print(f"正在安装 {package}...")
            subprocess.check_call([sys.executable, "-m", "pip", "install", package])
            print(f"✅ {package} 安装成功")
        except subprocess.CalledProcessError:
            print(f"❌ {package} 安装失败")


def main():
    """主测试函数"""
    print("Cloudflare绕过方法测试")
    print("=" * 60)
    
    # 询问是否安装依赖
    install = input("是否需要安装绕过工具依赖？(y/N): ").strip().lower()
    if install in ['y', 'yes']:
        install_dependencies()
        print("\n" + "=" * 60)
    
    results = []
    
    # 测试各种方法
    print("开始测试各种Cloudflare绕过方法...")
    print("注意: 测试过程可能需要几分钟时间\n")
    
    # 测试1: undetected-chromedriver
    success1, method1 = test_undetected_chromedriver()
    results.append((success1, method1))
    
    time.sleep(2)  # 间隔
    
    # 测试2: cloudscraper
    success2, method2 = test_cloudscraper()
    results.append((success2, method2))
    
    time.sleep(2)  # 间隔
    
    # 测试3: 普通Selenium（对比）
    success3, method3 = test_regular_selenium()
    results.append((success3, method3))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    successful_methods = []
    failed_methods = []
    
    for success, method in results:
        if success:
            successful_methods.append(method)
            print(f"✅ {method} - 成功")
        else:
            failed_methods.append(method)
            print(f"❌ {method} - 失败")
    
    print("\n🎯 推荐使用方案:")
    if successful_methods:
        print(f"推荐使用: {successful_methods[0]}")
        
        if "undetected-chromedriver" in successful_methods:
            print("\n💡 undetected-chromedriver 优势:")
            print("- 最强的反检测能力")
            print("- 成功率最高")
            print("- 支持复杂的JavaScript验证")
            
        elif "cloudscraper" in successful_methods:
            print("\n💡 cloudscraper 优势:")
            print("- 轻量级，不需要浏览器")
            print("- 速度较快")
            print("- 资源占用少")
            
        elif "普通Selenium" in successful_methods:
            print("\n💡 普通Selenium:")
            print("- 基础方案")
            print("- 可能需要手动处理验证")
    else:
        print("❌ 所有方法都失败了")
        print("可能的原因:")
        print("- 网络问题")
        print("- Cloudflare策略更新")
        print("- 需要更高级的绕过技术")
    
    print(f"\n📊 成功率: {len(successful_methods)}/{len(results)}")


if __name__ == "__main__":
    main()
