#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试HTML结构，查看网站的实际内容
"""

from selenium_spider import SeleniumCZBooksSpider
from bs4 import BeautifulSoup
import re


def debug_html_structure():
    """调试HTML结构"""
    print("=" * 60)
    print("调试网站HTML结构")
    print("=" * 60)
    
    try:
        with SeleniumCZBooksSpider(headless=False, delay=3) as spider:
            print("正在访问首页...")
            soup = spider.get_page("https://czbooks.net")
            
            if soup:
                print("✅ 成功获取页面")
                
                # 保存HTML到文件以便查看
                with open('debug_page.html', 'w', encoding='utf-8') as f:
                    f.write(str(soup))
                print("📄 页面HTML已保存到 debug_page.html")
                
                # 查找可能的小说容器
                print("\n🔍 查找可能的小说容器...")
                
                # 查找所有包含小说相关文本的元素
                novel_indicators = [
                    '小說', '小说', '作者', '最新', '章', '完結', '完结', 
                    '連載', '连载', '排行', '熱門', '热门'
                ]
                
                for indicator in novel_indicators:
                    elements = soup.find_all(text=re.compile(indicator))
                    if elements:
                        print(f"\n找到包含 '{indicator}' 的元素: {len(elements)} 个")
                        for i, elem in enumerate(elements[:3]):  # 只显示前3个
                            parent = elem.parent if elem.parent else elem
                            print(f"  {i+1}. {elem.strip()[:50]}...")
                            if parent.name:
                                print(f"     父元素: <{parent.name}> class={parent.get('class', [])}")
                
                # 查找所有链接
                print(f"\n🔗 查找所有链接...")
                all_links = soup.find_all('a', href=True)
                novel_links = []
                
                for link in all_links:
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    
                    # 查找可能是小说的链接
                    if ('/n/' in href or 
                        any(indicator in text for indicator in ['第', '章']) or
                        len(text) > 2 and len(text) < 50):
                        novel_links.append({
                            'text': text,
                            'href': href,
                            'parent_class': link.parent.get('class', []) if link.parent else []
                        })
                
                print(f"找到 {len(novel_links)} 个可能的小说链接")
                
                # 显示前10个
                for i, link in enumerate(novel_links[:10]):
                    print(f"  {i+1}. {link['text'][:30]} -> {link['href']}")
                    if link['parent_class']:
                        print(f"     父元素class: {link['parent_class']}")
                
                # 查找排行榜或列表容器
                print(f"\n📊 查找排行榜容器...")
                ranking_selectors = [
                    'div[class*="ranking"]',
                    'div[class*="list"]',
                    'div[class*="item"]',
                    'ul[class*="novel"]',
                    'ol[class*="rank"]'
                ]
                
                for selector in ranking_selectors:
                    elements = soup.select(selector)
                    if elements:
                        print(f"找到 {selector}: {len(elements)} 个")
                        for i, elem in enumerate(elements[:3]):
                            print(f"  {i+1}. class={elem.get('class', [])} 内容长度={len(elem.get_text())}")
                
                # 分析页面结构
                print(f"\n🏗️ 页面结构分析...")
                
                # 查找主要内容区域
                main_content = soup.find('main') or soup.find('div', class_=re.compile(r'content|main'))
                if main_content:
                    print("找到主要内容区域")
                    # 在主要内容区域中查找小说
                    content_links = main_content.find_all('a', href=True)
                    print(f"主要内容区域包含 {len(content_links)} 个链接")
                
                return True
            else:
                print("❌ 获取页面失败")
                return False
                
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        return False


if __name__ == "__main__":
    debug_html_structure()
