#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Selenium爬虫功能
"""

from selenium_spider import SeleniumCZBooksSpider
import time


def test_selenium_basic():
    """测试Selenium基本功能"""
    print("=" * 60)
    print("测试Selenium爬虫基本功能")
    print("=" * 60)
    
    try:
        # 创建爬虫实例（使用有头模式，可以看到浏览器操作）
        spider = SeleniumCZBooksSpider(headless=False, delay=3)
        
        print("正在启动浏览器...")
        spider.start_driver()
        
        print("✅ 浏览器启动成功")
        
        # 测试访问首页
        print("\n正在测试访问首页...")
        soup = spider.get_page("https://czbooks.net")
        
        if soup:
            print("✅ 成功访问首页")
            
            # 查找页面标题
            title = soup.find('title')
            if title:
                print(f"页面标题: {title.get_text()}")
            
            # 查找小说链接
            novel_links = soup.find_all('a', href=True)
            novel_count = 0
            for link in novel_links:
                href = link.get('href', '')
                if '/n/' in href:
                    novel_count += 1
                    if novel_count <= 3:
                        print(f"找到小说: {link.get_text(strip=True)}")
            
            print(f"总共找到 {novel_count} 个小说链接")
            
            if novel_count > 0:
                print("✅ 页面解析成功")
                return True
            else:
                print("❌ 未找到小说链接")
                return False
        else:
            print("❌ 访问首页失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False
    finally:
        try:
            spider.close_driver()
        except:
            pass


def test_novel_list():
    """测试获取小说列表"""
    print("\n" + "=" * 60)
    print("测试获取小说列表功能")
    print("=" * 60)
    
    try:
        with SeleniumCZBooksSpider(headless=False, delay=2) as spider:
            print("正在获取小说列表...")
            novels = spider.get_novel_list()
            
            if novels:
                print(f"✅ 成功获取到 {len(novels)} 本小说")
                
                print("\n前5本小说:")
                for i, novel in enumerate(novels[:5], 1):
                    print(f"{i}. 《{novel['title']}》")
                    print(f"   作者: {novel['author']}")
                    print(f"   状态: {novel['status']}")
                    print(f"   链接: {novel['url']}")
                    print()
                
                return novels[0] if novels else None
            else:
                print("❌ 未获取到小说列表")
                return None
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None


def test_novel_detail(novel_url):
    """测试获取小说详情"""
    print("\n" + "=" * 60)
    print("测试获取小说详情功能")
    print("=" * 60)
    
    if not novel_url:
        print("❌ 没有提供小说URL")
        return None
    
    try:
        with SeleniumCZBooksSpider(headless=False, delay=2) as spider:
            print(f"正在获取小说详情: {novel_url}")
            detail = spider.get_novel_detail(novel_url)
            
            if detail:
                print("✅ 成功获取小说详情")
                print(f"标题: {detail['title']}")
                print(f"作者: {detail['author']}")
                print(f"章节数: {detail['total_chapters']}")
                
                if detail.get('intro'):
                    print(f"简介: {detail['intro'][:100]}...")
                
                if detail['chapters']:
                    print(f"\n前3个章节:")
                    for i, chapter in enumerate(detail['chapters'][:3], 1):
                        print(f"  {i}. {chapter['title']}")
                    
                    return detail['chapters'][0]['url'] if detail['chapters'] else None
                else:
                    print("❌ 未找到章节列表")
                    return None
            else:
                print("❌ 获取小说详情失败")
                return None
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return None


def test_chapter_content(chapter_url):
    """测试获取章节内容"""
    print("\n" + "=" * 60)
    print("测试获取章节内容功能")
    print("=" * 60)
    
    if not chapter_url:
        print("❌ 没有提供章节URL")
        return False
    
    try:
        with SeleniumCZBooksSpider(headless=False, delay=2) as spider:
            print(f"正在获取章节内容: {chapter_url}")
            chapter = spider.get_chapter_content(chapter_url)
            
            if chapter:
                print("✅ 成功获取章节内容")
                print(f"章节标题: {chapter['title']}")
                print(f"内容长度: {len(chapter['content'])} 字符")
                
                if chapter['content']:
                    print(f"\n内容预览:")
                    preview = chapter['content'][:200] + "..." if len(chapter['content']) > 200 else chapter['content']
                    print(preview)
                    return True
                else:
                    print("❌ 章节内容为空")
                    return False
            else:
                print("❌ 获取章节内容失败")
                return False
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("CZBooks Selenium爬虫测试")
    print("=" * 60)
    print("注意: 测试过程中会打开浏览器窗口，请不要关闭")
    print("=" * 60)
    
    # 测试1: 基本功能
    print("\n🔍 开始测试...")
    basic_success = test_selenium_basic()
    
    if not basic_success:
        print("\n❌ 基本功能测试失败，停止后续测试")
        return
    
    # 测试2: 小说列表
    first_novel = test_novel_list()
    
    if not first_novel:
        print("\n❌ 小说列表测试失败，停止后续测试")
        return
    
    # 测试3: 小说详情
    first_chapter_url = test_novel_detail(first_novel['url'])
    
    if not first_chapter_url:
        print("\n❌ 小说详情测试失败，跳过章节内容测试")
    else:
        # 测试4: 章节内容
        test_chapter_content(first_chapter_url)
    
    print("\n" + "=" * 60)
    print("🎉 测试完成！")
    print("=" * 60)
    
    print("\n📋 测试总结:")
    print("- 如果所有测试都通过，说明爬虫功能正常")
    print("- 如果遇到Cloudflare挑战，浏览器会显示验证页面")
    print("- 可以手动完成验证，然后爬虫会继续运行")
    print("- 建议在实际使用时适当增加延时时间")


if __name__ == "__main__":
    main()
