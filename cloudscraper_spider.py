#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
使用cloudscraper绕过Cloudflare的HTTP爬虫
这是一个轻量级的解决方案，不需要浏览器
"""

import time
import os
import json
import re
from bs4 import BeautifulSoup
from tqdm import tqdm
from urllib.parse import urljoin

try:
    import cloudscraper
    CLOUDSCRAPER_AVAILABLE = True
except ImportError:
    CLOUDSCRAPER_AVAILABLE = False
    print("cloudscraper未安装，请运行: pip install cloudscraper")


class CloudscraperCZBooksSpider:
    """使用cloudscraper的Cloudflare绕过爬虫"""
    
    def __init__(self, base_url="https://czbooks.net", delay=2):
        """
        初始化cloudscraper爬虫
        
        Args:
            base_url (str): 网站基础URL
            delay (int): 请求间隔时间（秒）
        """
        if not CLOUDSCRAPER_AVAILABLE:
            raise ImportError("请先安装cloudscraper: pip install cloudscraper")
        
        self.base_url = base_url
        self.delay = delay
        
        # 创建cloudscraper会话
        self.scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'darwin',  # macOS
                'desktop': True
            }
        )
        
        # 设置请求头
        self.scraper.headers.update({
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
    
    def get_page(self, url, max_retries=3):
        """
        获取网页内容，自动绕过Cloudflare
        
        Args:
            url (str): 目标URL
            max_retries (int): 最大重试次数
            
        Returns:
            BeautifulSoup: 解析后的HTML对象
        """
        for attempt in range(max_retries):
            try:
                print(f"正在访问: {url} (尝试 {attempt + 1}/{max_retries})")
                
                response = self.scraper.get(url, timeout=15)
                response.raise_for_status()
                response.encoding = 'utf-8'
                
                # 检查是否成功绕过Cloudflare
                if 'cloudflare' in response.text.lower() and 'checking' in response.text.lower():
                    print("⚠️ 仍在Cloudflare挑战页面，重试...")
                    time.sleep(5)
                    continue
                
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 延时避免被封
                time.sleep(self.delay)
                
                print("✅ 成功获取页面")
                return soup
                
            except Exception as e:
                print(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + 1
                    time.sleep(wait_time)
                else:
                    print(f"获取页面失败: {url}")
                    return None
    
    def get_novel_list(self):
        """获取小说列表"""
        soup = self.get_page(self.base_url)
        if not soup:
            return []
        
        novels = []
        
        # 根据实际HTML结构查找小说列表
        novel_items = soup.find_all('li', class_='novel-item-wrapper')
        
        for item in novel_items:
            try:
                # 查找标题和链接
                title_elem = item.find('div', class_='novel-item-title')
                if not title_elem:
                    continue
                
                title_link = title_elem.find('a')
                if not title_link:
                    continue
                
                title = title_link.get_text(strip=True)
                novel_url = title_link.get('href')
                
                # 处理相对URL
                if novel_url and not novel_url.startswith('http'):
                    if novel_url.startswith('//'):
                        novel_url = 'https:' + novel_url
                    else:
                        novel_url = urljoin(self.base_url, novel_url)
                
                # 提取作者
                author = "未知作者"
                author_elem = item.find('div', class_='novel-item-author')
                if author_elem:
                    author_link = author_elem.find('a')
                    if author_link:
                        author = author_link.get_text(strip=True)
                
                # 提取状态
                status = "连载中"
                status_elem = item.find('div', class_='novel-item-state')
                if status_elem and '已完結' in status_elem.get_text():
                    status = "已完结"
                
                # 提取最新章节
                latest_chapter = ""
                latest_elem = item.find('div', class_='novel-item-newest-chapter')
                if latest_elem:
                    latest_link = latest_elem.find('a')
                    if latest_link:
                        latest_chapter = latest_link.get_text(strip=True)
                
                # 提取更新时间
                update_time = ""
                date_elem = item.find('div', class_='novel-item-date')
                if date_elem:
                    update_time = date_elem.get_text(strip=True)
                
                novels.append({
                    'title': title,
                    'author': author,
                    'url': novel_url,
                    'status': status,
                    'latest_chapter': latest_chapter,
                    'update_time': update_time
                })
                
            except Exception as e:
                print(f"解析小说信息失败: {e}")
                continue
        
        return novels
    
    def get_novel_detail(self, novel_url):
        """获取小说详情"""
        soup = self.get_page(novel_url)
        if not soup:
            return None
        
        try:
            # 提取标题
            title = "未知标题"
            title_elem = soup.find('h1') or soup.select_one('.novel-title')
            if title_elem:
                title = title_elem.get_text(strip=True)
            else:
                # 从页面标题中提取
                page_title = soup.find('title')
                if page_title:
                    page_title_text = page_title.get_text()
                    title_match = re.search(r'【(.+?)】', page_title_text)
                    if title_match:
                        title = title_match.group(1)
            
            # 提取作者
            author = "未知作者"
            author_text = soup.find(string=re.compile(r'作者[:：]'))
            if author_text:
                parent = author_text.parent if author_text.parent else author_text
                author_match = re.search(r'作者[:：]\s*(.+)', parent.get_text())
                if author_match:
                    author = author_match.group(1).strip()
            
            # 提取简介
            intro = ""
            intro_text = soup.find(string=re.compile(r'作品簡介|简介'))
            if intro_text:
                parent = intro_text.parent
                if parent:
                    intro_content = parent.get_text(strip=True)
                    intro_match = re.search(r'作品簡介[（(]文案[）)][:：]\s*(.+)', intro_content, re.DOTALL)
                    if intro_match:
                        intro = intro_match.group(1).strip()
            
            # 提取章节列表
            chapters = []
            chapter_links = soup.find_all('a', href=re.compile(r'chapterNumber=\d+'))
            
            for link in chapter_links:
                chapter_title = link.get_text(strip=True)
                chapter_url = link.get('href')
                
                # 修复URL拼接问题
                if chapter_url:
                    if chapter_url.startswith('//'):
                        chapter_url = 'https:' + chapter_url
                    elif not chapter_url.startswith('http'):
                        chapter_url = urljoin(self.base_url, chapter_url)
                
                if chapter_title and chapter_url:
                    chapters.append({
                        'title': chapter_title,
                        'url': chapter_url
                    })
            
            return {
                'title': title,
                'author': author,
                'intro': intro,
                'chapters': chapters,
                'total_chapters': len(chapters)
            }
            
        except Exception as e:
            print(f"解析小说详情失败: {e}")
            return None
    
    def get_chapter_content(self, chapter_url):
        """获取章节内容"""
        soup = self.get_page(chapter_url)
        if not soup:
            return None
        
        try:
            # 提取章节标题
            title = "未知章节"
            title_elem = soup.find('h1') or soup.select_one('.chapter-title')
            if title_elem:
                title = title_elem.get_text(strip=True)
            
            # 提取章节内容
            content = ""
            
            # 查找内容容器
            content_elem = None
            content_selectors = [
                'div.content',
                'div#content',
                'div.chapter-content',
                'div.text-content'
            ]
            
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    break
            
            # 如果没找到特定容器，查找包含大量文本的div
            if not content_elem:
                all_divs = soup.find_all('div')
                for div in all_divs:
                    text = div.get_text(strip=True)
                    if len(text) > 200 and not div.find('a') and not div.find('button'):
                        content_elem = div
                        break
            
            if content_elem:
                # 清理内容
                for script in content_elem(["script", "style", "nav", "header", "footer"]):
                    script.decompose()
                
                content = content_elem.get_text(separator='\n', strip=True)
                content = re.sub(r'\n\s*\n', '\n\n', content)
                content = content.strip()
            
            return {
                'title': title,
                'content': content,
                'url': chapter_url
            }
            
        except Exception as e:
            print(f"解析章节内容失败: {e}")
            return None
    
    def save_novel(self, novel_detail, output_dir="novels"):
        """保存小说"""
        if not novel_detail or not novel_detail.get('chapters'):
            print("没有有效的小说数据")
            return
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 清理文件名
        safe_title = re.sub(r'[<>:"/\\|?*]', '_', novel_detail['title'])
        novel_dir = os.path.join(output_dir, safe_title)
        os.makedirs(novel_dir, exist_ok=True)
        
        # 保存小说信息
        info_file = os.path.join(novel_dir, "info.json")
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump({
                'title': novel_detail['title'],
                'author': novel_detail['author'],
                'intro': novel_detail['intro'],
                'total_chapters': novel_detail['total_chapters']
            }, f, ensure_ascii=False, indent=2)
        
        # 下载章节内容
        print(f"开始下载小说: {novel_detail['title']}")
        
        for i, chapter in enumerate(tqdm(novel_detail['chapters'], desc="下载章节")):
            chapter_content = self.get_chapter_content(chapter['url'])
            
            if chapter_content:
                # 保存章节
                chapter_filename = f"{i+1:04d}_{re.sub(r'[<>:\"/\\|?*]', '_', chapter['title'])}.txt"
                chapter_file = os.path.join(novel_dir, chapter_filename)
                
                with open(chapter_file, 'w', encoding='utf-8') as f:
                    f.write(f"章节标题: {chapter_content['title']}\n")
                    f.write(f"章节链接: {chapter_content['url']}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(chapter_content['content'])
        
        print(f"小说下载完成: {novel_dir}")


if __name__ == "__main__":
    # 使用示例
    if CLOUDSCRAPER_AVAILABLE:
        spider = CloudscraperCZBooksSpider(delay=2)
        
        # 获取小说列表
        novels = spider.get_novel_list()
        if novels:
            print(f"找到 {len(novels)} 本小说")
            for i, novel in enumerate(novels[:5]):
                print(f"{i+1}. 《{novel['title']}》 - {novel['author']}")
        else:
            print("未找到小说列表")
    else:
        print("请先安装cloudscraper: pip install cloudscraper")
