#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CZBooks小说网Selenium爬虫
用于绕过Cloudflare保护机制
"""

import time
import os
import json
import re
from bs4 import BeautifulSoup
from tqdm import tqdm

try:
    from selenium import webdriver
    from selenium.webdriver.chrome.service import Service
    from selenium.webdriver.common.by import By
    from selenium.webdriver.support.ui import WebDriverWait
    from selenium.webdriver.support import expected_conditions as EC
    from selenium.webdriver.chrome.options import Options
    from webdriver_manager.chrome import ChromeDriverManager
    SELENIUM_AVAILABLE = True
except ImportError:
    SELENIUM_AVAILABLE = False
    print("Selenium未安装，请运行: pip install selenium webdriver-manager")


class SeleniumCZBooksSpider:
    """使用Selenium的CZBooks爬虫"""
    
    def __init__(self, base_url="https://czbooks.net", delay=2, headless=True):
        """
        初始化Selenium爬虫
        
        Args:
            base_url (str): 网站基础URL
            delay (int): 请求间隔时间（秒）
            headless (bool): 是否使用无头模式
        """
        if not SELENIUM_AVAILABLE:
            raise ImportError("请先安装Selenium: pip install selenium webdriver-manager")
        
        self.base_url = base_url
        self.delay = delay
        self.driver = None
        self.headless = headless
        
    def start_driver(self):
        """启动浏览器驱动"""
        if self.driver:
            return
        
        print("正在启动浏览器...")
        
        # 设置Chrome选项
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument('--headless')
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36')
        
        # 创建驱动
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        self.driver.implicitly_wait(10)
        
        print("浏览器启动成功")
    
    def close_driver(self):
        """关闭浏览器驱动"""
        if self.driver:
            self.driver.quit()
            self.driver = None
            print("浏览器已关闭")
    
    def get_page(self, url, wait_time=5):
        """
        获取网页内容
        
        Args:
            url (str): 目标URL
            wait_time (int): 等待时间
            
        Returns:
            BeautifulSoup: 解析后的HTML对象
        """
        if not self.driver:
            self.start_driver()
        
        try:
            print(f"正在访问: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(wait_time)
            
            # 检查是否有Cloudflare挑战页面
            if "Checking your browser" in self.driver.page_source:
                print("检测到Cloudflare挑战，等待通过...")
                time.sleep(10)  # 等待更长时间
            
            # 获取页面源码
            page_source = self.driver.page_source
            soup = BeautifulSoup(page_source, 'html.parser')
            
            # 延时
            time.sleep(self.delay)
            
            return soup
            
        except Exception as e:
            print(f"获取页面失败: {e}")
            return None
    
    def get_novel_list(self):
        """获取小说列表"""
        soup = self.get_page(self.base_url)
        if not soup:
            return []
        
        novels = []
        
        # 查找小说列表
        # 根据实际HTML结构调整选择器
        novel_items = soup.find_all('div', class_='ranking-item') or soup.find_all('div', class_='item')
        
        for item in novel_items:
            try:
                # 查找标题和链接
                title_link = item.find('a')
                if not title_link:
                    continue
                
                title = title_link.get_text(strip=True)
                novel_url = title_link.get('href')
                if novel_url and not novel_url.startswith('http'):
                    novel_url = self.base_url + novel_url
                
                # 提取作者
                author = "未知作者"
                author_text = item.find(text=re.compile(r'作者[:：]'))
                if author_text:
                    parent = author_text.parent if author_text.parent else author_text
                    author_match = re.search(r'作者[:：]\s*(.+)', parent.get_text())
                    if author_match:
                        author = author_match.group(1).strip()
                
                # 提取状态
                status = "连载中"
                if item.find(text=re.compile(r'已完結|完结')):
                    status = "已完结"
                
                novels.append({
                    'title': title,
                    'author': author,
                    'url': novel_url,
                    'status': status
                })
                
            except Exception as e:
                print(f"解析小说信息失败: {e}")
                continue
        
        return novels
    
    def get_novel_detail(self, novel_url):
        """获取小说详情"""
        soup = self.get_page(novel_url)
        if not soup:
            return None
        
        try:
            # 提取标题
            title = "未知标题"
            title_elem = soup.find('h1') or soup.select_one('.novel-title')
            if title_elem:
                title = title_elem.get_text(strip=True)
            
            # 提取作者
            author = "未知作者"
            author_text = soup.find(text=re.compile(r'作者[:：]'))
            if author_text:
                parent = author_text.parent if author_text.parent else author_text
                author_match = re.search(r'作者[:：]\s*(.+)', parent.get_text())
                if author_match:
                    author = author_match.group(1).strip()
            
            # 提取简介
            intro = ""
            intro_text = soup.find(text=re.compile(r'作品簡介|简介'))
            if intro_text:
                parent = intro_text.parent
                if parent:
                    intro_content = parent.get_text(strip=True)
                    intro_match = re.search(r'作品簡介[（(]文案[）)][:：]\s*(.+)', intro_content, re.DOTALL)
                    if intro_match:
                        intro = intro_match.group(1).strip()
            
            # 提取章节列表
            chapters = []
            chapter_links = soup.find_all('a', href=re.compile(r'chapterNumber=\d+'))
            
            for link in chapter_links:
                chapter_title = link.get_text(strip=True)
                chapter_url = link.get('href')
                if chapter_url and not chapter_url.startswith('http'):
                    chapter_url = self.base_url + chapter_url
                
                if chapter_title and chapter_url:
                    chapters.append({
                        'title': chapter_title,
                        'url': chapter_url
                    })
            
            return {
                'title': title,
                'author': author,
                'intro': intro,
                'chapters': chapters,
                'total_chapters': len(chapters)
            }
            
        except Exception as e:
            print(f"解析小说详情失败: {e}")
            return None
    
    def get_chapter_content(self, chapter_url):
        """获取章节内容"""
        soup = self.get_page(chapter_url)
        if not soup:
            return None
        
        try:
            # 提取章节标题
            title = "未知章节"
            title_elem = soup.find('h1') or soup.select_one('.chapter-title')
            if title_elem:
                title = title_elem.get_text(strip=True)
            
            # 提取章节内容
            content = ""
            
            # 查找内容容器
            content_elem = None
            content_selectors = [
                'div.content',
                'div#content',
                'div.chapter-content',
                'div.text-content'
            ]
            
            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    break
            
            # 如果没找到特定容器，查找包含大量文本的div
            if not content_elem:
                all_divs = soup.find_all('div')
                for div in all_divs:
                    text = div.get_text(strip=True)
                    if len(text) > 200 and not div.find('a') and not div.find('button'):
                        content_elem = div
                        break
            
            if content_elem:
                # 清理内容
                for script in content_elem(["script", "style", "nav", "header", "footer"]):
                    script.decompose()
                
                content = content_elem.get_text(separator='\n', strip=True)
                content = re.sub(r'\n\s*\n', '\n\n', content)
                content = content.strip()
            
            return {
                'title': title,
                'content': content,
                'url': chapter_url
            }
            
        except Exception as e:
            print(f"解析章节内容失败: {e}")
            return None
    
    def save_novel(self, novel_detail, output_dir="novels"):
        """保存小说"""
        if not novel_detail or not novel_detail.get('chapters'):
            print("没有有效的小说数据")
            return
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 清理文件名
        safe_title = re.sub(r'[<>:"/\\|?*]', '_', novel_detail['title'])
        novel_dir = os.path.join(output_dir, safe_title)
        os.makedirs(novel_dir, exist_ok=True)
        
        # 保存小说信息
        info_file = os.path.join(novel_dir, "info.json")
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump({
                'title': novel_detail['title'],
                'author': novel_detail['author'],
                'intro': novel_detail['intro'],
                'total_chapters': novel_detail['total_chapters']
            }, f, ensure_ascii=False, indent=2)
        
        # 下载章节内容
        print(f"开始下载小说: {novel_detail['title']}")
        
        for i, chapter in enumerate(tqdm(novel_detail['chapters'], desc="下载章节")):
            chapter_content = self.get_chapter_content(chapter['url'])
            
            if chapter_content:
                # 保存章节
                chapter_filename = f"{i+1:04d}_{re.sub(r'[<>:\"/\\|?*]', '_', chapter['title'])}.txt"
                chapter_file = os.path.join(novel_dir, chapter_filename)
                
                with open(chapter_file, 'w', encoding='utf-8') as f:
                    f.write(f"章节标题: {chapter_content['title']}\n")
                    f.write(f"章节链接: {chapter_content['url']}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(chapter_content['content'])
        
        print(f"小说下载完成: {novel_dir}")
    
    def __enter__(self):
        """上下文管理器入口"""
        self.start_driver()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.close_driver()


if __name__ == "__main__":
    # 使用示例
    if SELENIUM_AVAILABLE:
        with SeleniumCZBooksSpider(headless=False) as spider:
            # 获取小说列表
            novels = spider.get_novel_list()
            if novels:
                print(f"找到 {len(novels)} 本小说")
                for i, novel in enumerate(novels[:5]):
                    print(f"{i+1}. 《{novel['title']}》 - {novel['author']}")
            else:
                print("未找到小说列表")
    else:
        print("请先安装Selenium: pip install selenium webdriver-manager")
