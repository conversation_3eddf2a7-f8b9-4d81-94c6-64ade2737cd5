#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试脚本，用于验证网站访问
"""

import requests
import time
from bs4 import BeautifulSoup


def test_basic_access():
    """测试基本访问"""
    print("测试基本网站访问...")
    
    # 创建会话
    session = requests.Session()
    
    # 设置更完整的请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
        'DNT': '1'
    }
    
    session.headers.update(headers)
    
    try:
        print("正在访问 https://czbooks.net ...")
        response = session.get('https://czbooks.net', timeout=15)
        
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✅ 访问成功！")
            
            # 解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')
            title = soup.find('title')
            if title:
                print(f"页面标题: {title.get_text()}")
            
            # 查找小说相关内容
            novel_links = soup.find_all('a', href=True)
            novel_count = 0
            for link in novel_links:
                href = link.get('href', '')
                if '/n/' in href:  # 小说链接通常包含 /n/
                    novel_count += 1
                    if novel_count <= 3:  # 只显示前3个
                        print(f"找到小说链接: {link.get_text(strip=True)} - {href}")
            
            print(f"总共找到 {novel_count} 个小说链接")
            
            return True
            
        else:
            print(f"❌ 访问失败，状态码: {response.status_code}")
            print(f"响应内容: {response.text[:500]}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_with_different_methods():
    """尝试不同的访问方法"""
    print("\n" + "="*50)
    print("尝试不同的访问方法")
    print("="*50)
    
    methods = [
        {
            'name': '方法1: 标准Chrome浏览器',
            'headers': {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
            }
        },
        {
            'name': '方法2: Firefox浏览器',
            'headers': {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10.15; rv:109.0) Gecko/20100101 Firefox/121.0',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.8,zh-TW;q=0.7,zh-HK;q=0.5,en-US;q=0.3,en;q=0.2',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
            }
        },
        {
            'name': '方法3: Safari浏览器',
            'headers': {
                'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
                'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh-Hans;q=0.9',
                'Accept-Encoding': 'gzip, deflate, br',
                'Connection': 'keep-alive',
            }
        }
    ]
    
    for method in methods:
        print(f"\n尝试 {method['name']}...")
        
        session = requests.Session()
        session.headers.update(method['headers'])
        
        try:
            response = session.get('https://czbooks.net', timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                print("✅ 成功！")
                soup = BeautifulSoup(response.text, 'html.parser')
                title = soup.find('title')
                if title:
                    print(f"页面标题: {title.get_text()}")
                return True
            else:
                print(f"❌ 失败，状态码: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 异常: {e}")
        
        time.sleep(2)  # 延时
    
    return False


def test_specific_novel_page():
    """测试访问特定小说页面"""
    print("\n" + "="*50)
    print("测试访问特定小说页面")
    print("="*50)
    
    # 尝试访问之前找到的小说页面
    novel_url = "https://czbooks.net/n/ckjenm"  # 折腰
    
    session = requests.Session()
    session.headers.update({
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Referer': 'https://czbooks.net/',
    })
    
    try:
        print(f"访问: {novel_url}")
        response = session.get(novel_url, timeout=15)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 小说页面访问成功！")
            
            soup = BeautifulSoup(response.text, 'html.parser')
            
            # 查找小说标题
            title_elem = soup.find('h1') or soup.select_one('.novel-title')
            if title_elem:
                print(f"小说标题: {title_elem.get_text(strip=True)}")
            
            # 查找作者
            author_text = soup.find(text=lambda text: text and '作者:' in text)
            if author_text:
                print(f"作者信息: {author_text.strip()}")
            
            # 查找章节链接
            chapter_links = soup.find_all('a', href=lambda href: href and 'chapterNumber=' in href)
            print(f"找到 {len(chapter_links)} 个章节链接")
            
            if chapter_links:
                print("前3个章节:")
                for i, link in enumerate(chapter_links[:3]):
                    print(f"  {i+1}. {link.get_text(strip=True)}")
            
            return True
        else:
            print(f"❌ 访问失败，状态码: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 访问异常: {e}")
        return False


def main():
    """主函数"""
    print("CZBooks网站访问测试")
    print("="*60)
    
    # 测试1: 基本访问
    success1 = test_basic_access()
    
    if not success1:
        # 测试2: 尝试不同方法
        success2 = test_with_different_methods()
        
        if not success2:
            print("\n❌ 所有访问方法都失败了")
            print("可能的原因:")
            print("1. 网站有严格的反爬虫机制")
            print("2. 需要特定的Cookie或认证")
            print("3. IP被封禁")
            print("4. 网站暂时不可用")
            return
    
    # 测试3: 访问特定小说页面
    test_specific_novel_page()
    
    print("\n" + "="*60)
    print("测试完成")
    print("="*60)


if __name__ == "__main__":
    main()
