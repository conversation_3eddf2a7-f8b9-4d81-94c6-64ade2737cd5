#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CZBooks小说网爬虫
支持爬取小说列表、小说详情和章节内容
"""

import requests
import time
import re
import os
from bs4 import BeautifulSoup
from fake_useragent import UserAgent
from urllib.parse import urljoin, urlparse
from tqdm import tqdm
import json


class CZBooksSpider:
    """CZBooks小说网爬虫类"""
    
    def __init__(self, base_url="https://czbooks.net", delay=1):
        """
        初始化爬虫

        Args:
            base_url (str): 网站基础URL
            delay (int): 请求间隔时间（秒）
        """
        self.base_url = base_url
        self.delay = delay
        self.session = requests.Session()
        self.ua = UserAgent()

        # 设置更真实的请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHT<PERSON>, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Cache-Control': 'max-age=0',
            'DNT': '1',
            'Sec-GPC': '1'
        })

        # 设置会话配置
        self.session.verify = True
        self.session.max_redirects = 5
    
    def get_page(self, url, max_retries=3):
        """
        获取网页内容
        
        Args:
            url (str): 目标URL
            max_retries (int): 最大重试次数
            
        Returns:
            BeautifulSoup: 解析后的HTML对象，失败返回None
        """
        for attempt in range(max_retries):
            try:
                # 更新User-Agent，使用更真实的浏览器标识
                user_agents = [
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/17.1 Safari/605.1.15',
                    'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0'
                ]
                import random
                self.session.headers['User-Agent'] = random.choice(user_agents)

                # 添加Referer头
                if 'czbooks.net' in url and url != self.base_url:
                    self.session.headers['Referer'] = self.base_url

                response = self.session.get(url, timeout=15, allow_redirects=True)
                response.raise_for_status()
                response.encoding = 'utf-8'

                soup = BeautifulSoup(response.text, 'lxml')

                # 延时避免被封
                time.sleep(self.delay)

                return soup

            except requests.exceptions.RequestException as e:
                print(f"请求失败 (尝试 {attempt + 1}/{max_retries}): {e}")
                if attempt < max_retries - 1:
                    wait_time = (2 ** attempt) + random.uniform(1, 3)  # 指数退避 + 随机延时
                    time.sleep(wait_time)
                else:
                    print(f"获取页面失败: {url}")
                    return None
    
    def get_novel_list(self, page=1):
        """
        获取小说列表

        Args:
            page (int): 页码

        Returns:
            list: 小说信息列表
        """
        if page == 1:
            url = self.base_url
        else:
            url = f"{self.base_url}/page/{page}"

        soup = self.get_page(url)
        if not soup:
            return []

        novels = []

        # 根据实际HTML结构查找小说列表
        # 首页的小说列表在排行榜中
        ranking_items = soup.find_all('div', class_='ranking-item')

        for item in ranking_items:
            try:
                # 查找小说标题和链接
                title_link = item.find('a')
                if not title_link:
                    continue

                title = title_link.get_text(strip=True)
                novel_url = urljoin(self.base_url, title_link.get('href', ''))

                # 提取作者信息
                author = "未知作者"
                author_elem = item.find(text=re.compile(r'作者[:：]'))
                if author_elem:
                    # 查找包含"作者:"的文本
                    parent = author_elem.parent if author_elem.parent else author_elem
                    author_text = parent.get_text(strip=True)
                    author_match = re.search(r'作者[:：]\s*(.+)', author_text)
                    if author_match:
                        author = author_match.group(1).strip()

                # 提取状态信息
                status = "连载中"
                status_elem = item.find(text=re.compile(r'已完結|完结'))
                if status_elem:
                    status = "已完结"

                # 提取最新章节
                latest_chapter = ""
                latest_elem = item.find(text=re.compile(r'最新[:：]'))
                if latest_elem:
                    parent = latest_elem.parent if latest_elem.parent else latest_elem
                    latest_text = parent.get_text(strip=True)
                    latest_match = re.search(r'最新[:：]\s*(.+)', latest_text)
                    if latest_match:
                        latest_chapter = latest_match.group(1).strip()

                # 提取更新时间
                update_time = ""
                time_elem = item.find(text=re.compile(r'\d{4}-\d{2}-\d{2}'))
                if time_elem:
                    update_time = time_elem.strip()

                novel_info = {
                    'title': title,
                    'author': author,
                    'url': novel_url,
                    'status': status,
                    'latest_chapter': latest_chapter,
                    'update_time': update_time
                }

                novels.append(novel_info)

            except Exception as e:
                print(f"解析小说信息失败: {e}")
                continue

        return novels
    
    def get_novel_detail(self, novel_url):
        """
        获取小说详细信息和章节列表

        Args:
            novel_url (str): 小说详情页URL

        Returns:
            dict: 小说详细信息
        """
        soup = self.get_page(novel_url)
        if not soup:
            return None

        try:
            # 提取小说标题
            title = "未知标题"
            title_elem = soup.find('h1') or soup.select_one('.novel-title')
            if title_elem:
                title = title_elem.get_text(strip=True)

            # 提取作者
            author = "未知作者"
            author_elem = soup.find(text=re.compile(r'作者[:：]'))
            if author_elem:
                # 查找包含作者信息的父元素
                parent = author_elem.parent if author_elem.parent else author_elem
                author_text = parent.get_text(strip=True)
                author_match = re.search(r'作者[:：]\s*(.+)', author_text)
                if author_match:
                    author = author_match.group(1).strip()

            # 提取简介
            intro = ""
            # 查找简介内容，通常在作品简介部分
            intro_text = soup.find(text=re.compile(r'作品簡介|简介'))
            if intro_text:
                # 查找简介后的内容
                parent = intro_text.parent
                if parent:
                    # 获取简介后的文本内容
                    intro_content = parent.get_text(strip=True)
                    # 提取简介部分
                    intro_match = re.search(r'作品簡介[（(]文案[）)][:：]\s*(.+)', intro_content, re.DOTALL)
                    if intro_match:
                        intro = intro_match.group(1).strip()

            # 提取章节列表
            chapters = []
            # 查找章节链接，根据实际HTML结构调整
            chapter_links = soup.find_all('a', href=re.compile(r'/[^/]+\?chapterNumber=\d+'))

            for link in chapter_links:
                chapter_title = link.get_text(strip=True)
                chapter_url = urljoin(self.base_url, link.get('href'))

                if chapter_title and chapter_url:
                    chapters.append({
                        'title': chapter_title,
                        'url': chapter_url
                    })

            # 提取状态信息
            status = "连载中"
            status_elem = soup.find(text=re.compile(r'已完結|完结'))
            if status_elem:
                status = "已完结"

            novel_detail = {
                'title': title,
                'author': author,
                'intro': intro,
                'status': status,
                'chapters': chapters,
                'total_chapters': len(chapters)
            }

            return novel_detail

        except Exception as e:
            print(f"解析小说详情失败: {e}")
            return None
    
    def get_chapter_content(self, chapter_url):
        """
        获取章节内容

        Args:
            chapter_url (str): 章节URL

        Returns:
            dict: 章节信息
        """
        soup = self.get_page(chapter_url)
        if not soup:
            return None

        try:
            # 提取章节标题
            title = "未知章节"
            title_elem = soup.find('h1') or soup.select_one('.chapter-title')
            if title_elem:
                title = title_elem.get_text(strip=True)

            # 提取章节内容
            content = ""

            # 根据实际HTML结构查找内容
            # 在czbooks.net中，章节内容通常在特定的div中
            content_elem = None

            # 尝试多种可能的内容容器选择器
            content_selectors = [
                'div.content',
                'div#content',
                'div.chapter-content',
                'div.text-content',
                'div.novel-content',
                'div.chapter-text',
                '.content-text'
            ]

            for selector in content_selectors:
                content_elem = soup.select_one(selector)
                if content_elem:
                    break

            # 如果没有找到特定容器，尝试查找包含大量文本的div
            if not content_elem:
                all_divs = soup.find_all('div')
                for div in all_divs:
                    text = div.get_text(strip=True)
                    # 如果div包含较多文本且不是导航或其他元素，可能是内容
                    if len(text) > 200 and not div.find('a') and not div.find('button'):
                        content_elem = div
                        break

            if content_elem:
                # 清理脚本和样式标签
                for script in content_elem(["script", "style", "nav", "header", "footer"]):
                    script.decompose()

                # 获取文本内容
                content = content_elem.get_text(separator='\n', strip=True)

                # 清理多余的空行和特殊字符
                content = re.sub(r'\n\s*\n', '\n\n', content)
                content = re.sub(r'^\s*\n', '', content)  # 移除开头的空行
                content = content.strip()

            return {
                'title': title,
                'content': content,
                'url': chapter_url
            }

        except Exception as e:
            print(f"解析章节内容失败: {e}")
            return None
    
    def save_novel(self, novel_detail, output_dir="novels"):
        """
        保存小说到文件
        
        Args:
            novel_detail (dict): 小说详细信息
            output_dir (str): 输出目录
        """
        if not novel_detail or not novel_detail.get('chapters'):
            print("没有有效的小说数据")
            return
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 清理文件名
        safe_title = re.sub(r'[<>:"/\\|?*]', '_', novel_detail['title'])
        novel_dir = os.path.join(output_dir, safe_title)
        os.makedirs(novel_dir, exist_ok=True)
        
        # 保存小说信息
        info_file = os.path.join(novel_dir, "info.json")
        with open(info_file, 'w', encoding='utf-8') as f:
            json.dump({
                'title': novel_detail['title'],
                'author': novel_detail['author'],
                'intro': novel_detail['intro'],
                'total_chapters': novel_detail['total_chapters']
            }, f, ensure_ascii=False, indent=2)
        
        # 下载章节内容
        print(f"开始下载小说: {novel_detail['title']}")
        
        for i, chapter in enumerate(tqdm(novel_detail['chapters'], desc="下载章节")):
            chapter_content = self.get_chapter_content(chapter['url'])
            
            if chapter_content:
                # 保存章节
                chapter_filename = f"{i+1:04d}_{re.sub(r'[<>:\"/\\|?*]', '_', chapter['title'])}.txt"
                chapter_file = os.path.join(novel_dir, chapter_filename)
                
                with open(chapter_file, 'w', encoding='utf-8') as f:
                    f.write(f"章节标题: {chapter_content['title']}\n")
                    f.write(f"章节链接: {chapter_content['url']}\n")
                    f.write("=" * 50 + "\n\n")
                    f.write(chapter_content['content'])
        
        print(f"小说下载完成: {novel_dir}")
