#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CZBooks小说网爬虫主程序
使用示例和演示
"""

import os
import sys
import json
from czbooks_spider import CZBooksSpider


def main():
    """主程序"""
    print("=" * 60)
    print("CZBooks小说网爬虫")
    print("=" * 60)
    
    # 创建爬虫实例
    spider = CZBooksSpider(delay=2)  # 设置2秒延时避免被封
    
    while True:
        print("\n请选择操作:")
        print("1. 获取热门小说列表")
        print("2. 搜索并下载指定小说")
        print("3. 通过URL下载小说")
        print("4. 下载单个章节")
        print("0. 退出")
        
        choice = input("\n请输入选择 (0-4): ").strip()
        
        if choice == "0":
            print("感谢使用！")
            break
        elif choice == "1":
            get_novel_list(spider)
        elif choice == "2":
            search_and_download_novel(spider)
        elif choice == "3":
            download_novel_by_url(spider)
        elif choice == "4":
            download_single_chapter(spider)
        else:
            print("无效选择，请重新输入！")


def get_novel_list(spider):
    """获取并显示小说列表"""
    print("\n正在获取热门小说列表...")
    
    try:
        novels = spider.get_novel_list()
        
        if not novels:
            print("未能获取到小说列表，请检查网络连接或稍后重试。")
            return
        
        print(f"\n找到 {len(novels)} 本小说:")
        print("-" * 80)
        
        for i, novel in enumerate(novels, 1):
            print(f"{i:2d}. 《{novel['title']}》")
            print(f"     作者: {novel['author']}")
            print(f"     状态: {novel['status']}")
            if novel.get('latest_chapter'):
                print(f"     最新: {novel['latest_chapter']}")
            if novel.get('update_time'):
                print(f"     更新: {novel['update_time']}")
            print(f"     链接: {novel['url']}")
            print()
        
        # 询问是否下载
        choice = input("是否要下载其中某本小说？(输入序号，或按回车跳过): ").strip()
        if choice.isdigit():
            index = int(choice) - 1
            if 0 <= index < len(novels):
                download_novel(spider, novels[index]['url'])
            else:
                print("序号超出范围！")
                
    except Exception as e:
        print(f"获取小说列表失败: {e}")


def search_and_download_novel(spider):
    """搜索并下载小说"""
    keyword = input("\n请输入小说名称或作者名: ").strip()
    if not keyword:
        print("搜索关键词不能为空！")
        return
    
    print(f"\n正在搜索包含 '{keyword}' 的小说...")
    
    try:
        # 获取小说列表
        novels = spider.get_novel_list()
        
        # 过滤匹配的小说
        matched_novels = []
        for novel in novels:
            if (keyword.lower() in novel['title'].lower() or 
                keyword.lower() in novel['author'].lower()):
                matched_novels.append(novel)
        
        if not matched_novels:
            print(f"未找到包含 '{keyword}' 的小说。")
            return
        
        print(f"\n找到 {len(matched_novels)} 本相关小说:")
        print("-" * 60)
        
        for i, novel in enumerate(matched_novels, 1):
            print(f"{i}. 《{novel['title']}》 - {novel['author']}")
            print(f"   状态: {novel['status']}")
            if novel.get('latest_chapter'):
                print(f"   最新: {novel['latest_chapter']}")
            print()
        
        choice = input("请选择要下载的小说 (输入序号): ").strip()
        if choice.isdigit():
            index = int(choice) - 1
            if 0 <= index < len(matched_novels):
                download_novel(spider, matched_novels[index]['url'])
            else:
                print("序号超出范围！")
        else:
            print("请输入有效的序号！")
            
    except Exception as e:
        print(f"搜索失败: {e}")


def download_novel_by_url(spider):
    """通过URL下载小说"""
    url = input("\n请输入小说详情页URL: ").strip()
    if not url:
        print("URL不能为空！")
        return
    
    if "czbooks.net" not in url:
        print("请输入有效的czbooks.net小说链接！")
        return
    
    download_novel(spider, url)


def download_novel(spider, novel_url):
    """下载小说"""
    print(f"\n正在获取小说详情...")
    
    try:
        # 获取小说详情
        novel_detail = spider.get_novel_detail(novel_url)
        
        if not novel_detail:
            print("获取小说详情失败！")
            return
        
        print(f"\n小说信息:")
        print(f"标题: {novel_detail['title']}")
        print(f"作者: {novel_detail['author']}")
        print(f"状态: {novel_detail.get('status', '未知')}")
        print(f"章节数: {novel_detail['total_chapters']}")
        if novel_detail.get('intro'):
            print(f"简介: {novel_detail['intro'][:200]}...")
        
        if novel_detail['total_chapters'] == 0:
            print("未找到章节列表，可能需要登录或该小说暂不支持爬取。")
            return
        
        # 确认下载
        confirm = input(f"\n确认下载《{novel_detail['title']}》({novel_detail['total_chapters']}章)？(y/N): ").strip().lower()
        if confirm in ['y', 'yes']:
            spider.save_novel(novel_detail)
        else:
            print("已取消下载。")
            
    except Exception as e:
        print(f"下载失败: {e}")


def download_single_chapter(spider):
    """下载单个章节"""
    url = input("\n请输入章节URL: ").strip()
    if not url:
        print("URL不能为空！")
        return
    
    if "czbooks.net" not in url:
        print("请输入有效的czbooks.net章节链接！")
        return
    
    print("\n正在下载章节...")
    
    try:
        chapter = spider.get_chapter_content(url)
        
        if not chapter:
            print("获取章节内容失败！")
            return
        
        print(f"\n章节标题: {chapter['title']}")
        print(f"内容长度: {len(chapter['content'])} 字符")
        
        if chapter['content']:
            # 保存章节
            filename = f"{chapter['title']}.txt".replace('/', '_').replace('\\', '_')
            filename = filename.replace(':', '_').replace('*', '_').replace('?', '_')
            filename = filename.replace('"', '_').replace('<', '_').replace('>', '_')
            filename = filename.replace('|', '_')
            
            with open(filename, 'w', encoding='utf-8') as f:
                f.write(f"章节标题: {chapter['title']}\n")
                f.write(f"章节链接: {chapter['url']}\n")
                f.write("=" * 50 + "\n\n")
                f.write(chapter['content'])
            
            print(f"章节已保存为: {filename}")
        else:
            print("章节内容为空！")
            
    except Exception as e:
        print(f"下载章节失败: {e}")


if __name__ == "__main__":
    main()
