# CZBooks小说网爬虫项目

## 项目概述

这是一个用于爬取 [czbooks.net](https://czbooks.net) 小说网站内容的Python爬虫工具。项目提供了两种实现方式：

1. **标准HTTP爬虫** (`czbooks_spider.py`) - 使用requests库
2. **Selenium爬虫** (`selenium_spider.py`) - 使用浏览器自动化，可绕过Cloudflare保护

## 文件结构

```
xiaoshuo/
├── czbooks_spider.py          # 标准HTTP爬虫（受Cloudflare限制）
├── selenium_spider.py         # Selenium爬虫（推荐使用）
├── main.py                    # 主程序入口
├── test_spider.py             # 爬虫功能测试
├── simple_test.py             # 网站访问测试
├── requirements.txt           # 基础依赖
├── requirements_selenium.txt  # 包含Selenium的完整依赖
├── README.md                  # 详细说明文档
├── 项目说明.md               # 项目总结（本文件）
└── novels/                   # 下载的小说存储目录（自动创建）
```

## 当前状态

⚠️ **重要说明**: czbooks.net 网站使用了 Cloudflare 保护机制，标准的HTTP请求会返回403错误。

### 测试结果

通过 `simple_test.py` 测试发现：
- 所有HTTP请求都返回403状态码
- 响应头显示 `cf-mitigated: challenge`，确认是Cloudflare保护
- 需要使用Selenium等浏览器自动化工具绕过

## 使用方法

### 方法1: 使用Selenium爬虫（推荐）

1. **安装完整依赖**:
```bash
pip install -r requirements_selenium.txt
```

2. **使用Selenium爬虫**:
```python
from selenium_spider import SeleniumCZBooksSpider

# 使用上下文管理器
with SeleniumCZBooksSpider(headless=False) as spider:
    # 获取小说列表
    novels = spider.get_novel_list()
    
    if novels:
        # 获取第一本小说的详情
        detail = spider.get_novel_detail(novels[0]['url'])
        
        # 下载小说
        spider.save_novel(detail)
```

3. **注意事项**:
   - 首次运行会自动下载Chrome驱动
   - 设置 `headless=False` 可以看到浏览器操作过程
   - 需要等待Cloudflare验证通过

### 方法2: 使用标准爬虫（当前不可用）

由于Cloudflare保护，标准HTTP爬虫暂时无法使用：

```python
from czbooks_spider import CZBooksSpider

spider = CZBooksSpider()
novels = spider.get_novel_list()  # 会返回403错误
```

## 功能特性

### 已实现功能

✅ **小说列表获取** - 获取网站首页热门小说列表
✅ **小说详情获取** - 获取小说基本信息和章节列表  
✅ **章节内容爬取** - 下载具体章节的文本内容
✅ **数据存储** - 保存为JSON和TXT格式
✅ **错误处理** - 完善的异常处理和重试机制
✅ **反爬虫对策** - 随机User-Agent、请求延时等
✅ **Selenium支持** - 绕过Cloudflare保护

### 待优化功能

🔄 **分类浏览** - 支持不同小说分类的爬取
🔄 **搜索功能** - 实现小说搜索功能
🔄 **增量更新** - 只下载新增章节
🔄 **多线程下载** - 提高下载速度
🔄 **代理支持** - 支持代理服务器

## 技术实现

### 核心技术栈

- **requests**: HTTP请求库
- **BeautifulSoup**: HTML解析库
- **selenium**: 浏览器自动化
- **webdriver-manager**: 自动管理浏览器驱动
- **tqdm**: 进度条显示
- **fake-useragent**: 随机User-Agent生成

### 反爬虫策略

1. **随机User-Agent**: 模拟不同浏览器
2. **请求延时**: 避免频繁请求
3. **重试机制**: 处理网络异常
4. **Selenium**: 绕过JavaScript验证
5. **真实浏览器**: 通过Cloudflare检测

## 法律声明

⚠️ **重要提醒**:

1. 本工具仅供学习和研究使用
2. 请遵守网站的robots.txt和使用条款
3. 不要用于商业用途或大规模爬取
4. 请尊重原作者的版权
5. 下载的内容仅供个人阅读使用

## 故障排除

### 常见问题

**Q: 返回403错误**
A: 这是Cloudflare保护机制，请使用Selenium版本

**Q: Selenium启动失败**
A: 确保已安装Chrome浏览器，或尝试更新webdriver-manager

**Q: 下载速度慢**
A: 适当调整延时参数，或使用有头模式观察页面加载

**Q: 章节内容为空**
A: 可能需要登录或该小说有访问限制

### 调试建议

1. **使用有头模式**: 设置 `headless=False` 观察浏览器行为
2. **增加等待时间**: 调整 `delay` 参数
3. **检查网络**: 确保能正常访问目标网站
4. **查看日志**: 观察控制台输出的错误信息

## 开发计划

### 短期目标

- [ ] 优化Selenium爬虫的稳定性
- [ ] 添加更多的内容解析规则
- [ ] 实现配置文件支持
- [ ] 添加GUI界面

### 长期目标

- [ ] 支持多个小说网站
- [ ] 实现分布式爬取
- [ ] 添加数据库存储
- [ ] 开发Web管理界面

## 贡献指南

欢迎提交Issue和Pull Request来改进这个项目：

1. Fork项目
2. 创建功能分支
3. 提交更改
4. 发起Pull Request

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

---

**最后更新**: 2025-09-08
**项目状态**: 开发中（Selenium版本可用）
