# CZBooks小说网爬虫

一个用于爬取 [czbooks.net](https://czbooks.net) 小说网站内容的Python爬虫工具。

## 功能特性

- 🔍 获取热门小说列表
- 📖 搜索指定小说
- 📚 下载完整小说（包含所有章节）
- 📄 下载单个章节
- 💾 自动保存为文本文件
- 🛡️ 内置反爬虫机制（随机User-Agent、请求延时等）
- 🔄 自动重试机制
- 📊 下载进度显示

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用方法

### 1. 运行主程序

```bash
python main.py
```

### 2. 选择操作

程序提供以下功能：

1. **获取热门小说列表** - 显示网站首页的热门小说
2. **搜索并下载指定小说** - 根据小说名或作者名搜索并下载
3. **通过URL下载小说** - 直接输入小说详情页URL进行下载
4. **下载单个章节** - 下载指定章节的内容

### 3. 编程接口使用

```python
from czbooks_spider import CZBooksSpider

# 创建爬虫实例
spider = CZBooksSpider(delay=2)  # 设置2秒延时

# 获取小说列表
novels = spider.get_novel_list()
for novel in novels:
    print(f"《{novel['title']}》 - {novel['author']}")

# 获取小说详情
novel_detail = spider.get_novel_detail("https://czbooks.net/n/ckjenm")
print(f"小说标题: {novel_detail['title']}")
print(f"章节数: {novel_detail['total_chapters']}")

# 下载小说
spider.save_novel(novel_detail)

# 获取单个章节内容
chapter = spider.get_chapter_content("https://czbooks.net/n/ckjenm/cnj7?chapterNumber=0")
print(f"章节标题: {chapter['title']}")
print(f"章节内容: {chapter['content'][:100]}...")
```

## 文件结构

```
xiaoshuo/
├── czbooks_spider.py    # 主要爬虫类
├── main.py             # 主程序入口
├── requirements.txt    # 依赖包列表
├── README.md          # 说明文档
└── novels/            # 下载的小说存储目录
    └── 小说名/
        ├── info.json  # 小说信息
        ├── 0001_第1章.txt
        ├── 0002_第2章.txt
        └── ...
```

## 配置说明

### 爬虫参数

```python
spider = CZBooksSpider(
    base_url="https://czbooks.net",  # 网站基础URL
    delay=2                          # 请求间隔时间（秒）
)
```

### 重要参数

- `delay`: 请求间隔时间，建议设置为1-3秒，避免被网站封禁
- `max_retries`: 最大重试次数，默认为3次
- `timeout`: 请求超时时间，默认为10秒

## 重要说明

⚠️ **当前状态**: 由于目标网站 czbooks.net 使用了 Cloudflare 保护机制，直接的HTTP请求会返回403错误。这是网站的反爬虫措施。

### 解决方案

1. **使用浏览器扩展**: 可以使用浏览器扩展来手动保存小说内容
2. **使用代理服务**: 通过代理服务器访问（需要额外配置）
3. **使用Selenium**: 使用浏览器自动化工具（需要安装浏览器驱动）
4. **等待网站政策变化**: 网站的反爬虫策略可能会调整

### Selenium版本实现

如果需要绕过Cloudflare保护，可以使用Selenium：

```bash
pip install selenium webdriver-manager
```

```python
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from selenium.webdriver.common.by import By
import time

# 创建浏览器实例
service = Service(ChromeDriverManager().install())
driver = webdriver.Chrome(service=service)

try:
    # 访问网站
    driver.get("https://czbooks.net")
    time.sleep(5)  # 等待页面加载

    # 获取页面内容
    page_source = driver.page_source
    print("页面获取成功")

finally:
    driver.quit()
```

## 注意事项

### 法律声明

⚠️ **重要提醒**：

1. 本工具仅供学习和研究使用
2. 请遵守网站的robots.txt和使用条款
3. 不要用于商业用途或大规模爬取
4. 请尊重原作者的版权
5. 建议适当设置延时，避免对服务器造成压力

### 使用建议

1. **合理设置延时**：建议设置1-3秒的请求间隔
2. **避免频繁请求**：不要同时运行多个爬虫实例
3. **尊重版权**：下载的内容仅供个人阅读使用
4. **网络环境**：确保网络连接稳定
5. **异常处理**：如遇到反爬虫机制，请适当增加延时

### 常见问题

**Q: 为什么获取不到小说列表？**
A: 网站使用了Cloudflare保护，需要使用Selenium或其他方法绕过。

**Q: 返回403错误怎么办？**
A: 这是Cloudflare的反爬虫机制，建议使用Selenium或等待网站政策变化。

**Q: 下载的章节内容为空？**
A: 可能需要登录才能查看完整内容，或者该小说有访问限制。

**Q: 如何下载特定分类的小说？**
A: 目前主要支持首页热门小说，如需特定分类，可以修改代码中的URL。

## 技术实现

### 主要技术栈

- **requests**: HTTP请求库
- **BeautifulSoup**: HTML解析库
- **fake-useragent**: 随机User-Agent生成
- **tqdm**: 进度条显示
- **lxml**: XML/HTML解析器

### 核心功能

1. **网页抓取**: 使用requests发送HTTP请求
2. **内容解析**: 使用BeautifulSoup解析HTML
3. **反爬虫**: 随机User-Agent、请求延时、重试机制
4. **数据存储**: 保存为JSON和TXT格式
5. **错误处理**: 完善的异常处理和重试机制

## 更新日志

### v1.0.0 (2025-09-08)
- 初始版本发布
- 支持基本的小说列表获取和下载功能
- 实现反爬虫机制
- 添加命令行交互界面

## 贡献

欢迎提交Issue和Pull Request来改进这个项目。

## 许可证

本项目仅供学习和研究使用，请勿用于商业用途。

---

**免责声明**: 本工具仅供技术学习和研究使用，使用者需自行承担使用风险，并遵守相关法律法规和网站条款。
