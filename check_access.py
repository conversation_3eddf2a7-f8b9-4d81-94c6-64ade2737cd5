#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查网站访问权限和cookie需求
"""

import requests
from selenium_spider import Selen<PERSON>CZBooksSpider
import time


def test_direct_requests():
    """测试直接HTTP请求"""
    print("=" * 60)
    print("测试直接HTTP请求（不使用Selenium）")
    print("=" * 60)
    
    session = requests.Session()
    
    # 设置完整的浏览器头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
        'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Sec-Fetch-User': '?1',
        'Cache-Control': 'max-age=0',
    }
    
    session.headers.update(headers)
    
    try:
        print("1. 测试首页访问...")
        response = session.get('https://czbooks.net', timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 首页可以直接访问！")
            print(f"响应长度: {len(response.text)} 字符")
            
            # 检查是否包含小说内容
            if '小說' in response.text or '小说' in response.text:
                print("✅ 包含小说内容")
            else:
                print("❌ 可能被重定向或内容被过滤")
            
            return True
        else:
            print(f"❌ 访问失败: {response.status_code}")
            print(f"响应内容: {response.text[:200]}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False


def test_with_selenium_cookies():
    """使用Selenium获取cookie，然后用于requests"""
    print("\n" + "=" * 60)
    print("测试使用Selenium获取Cookie")
    print("=" * 60)
    
    try:
        with SeleniumCZBooksSpider(headless=True, delay=2) as spider:
            print("1. 使用Selenium访问网站...")
            soup = spider.get_page("https://czbooks.net")
            
            if soup:
                print("✅ Selenium访问成功")
                
                # 获取cookies
                cookies = spider.driver.get_cookies()
                print(f"获取到 {len(cookies)} 个cookie:")
                
                for cookie in cookies:
                    print(f"  - {cookie['name']}: {cookie['value'][:20]}...")
                
                # 将cookies转换为requests格式
                cookie_dict = {cookie['name']: cookie['value'] for cookie in cookies}
                
                print("\n2. 使用获取的cookie进行requests请求...")
                session = requests.Session()
                session.headers.update({
                    'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                    'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
                    'Accept-Language': 'zh-CN,zh;q=0.9',
                    'Referer': 'https://czbooks.net/'
                })
                
                response = session.get('https://czbooks.net', cookies=cookie_dict, timeout=10)
                print(f"状态码: {response.status_code}")
                
                if response.status_code == 200:
                    print("✅ 使用cookie的requests请求成功！")
                    print(f"响应长度: {len(response.text)} 字符")
                    
                    # 测试小说页面
                    print("\n3. 测试访问具体小说页面...")
                    novel_url = "https://czbooks.net/n/ckjenm"  # 折腰
                    novel_response = session.get(novel_url, cookies=cookie_dict, timeout=10)
                    print(f"小说页面状态码: {novel_response.status_code}")
                    
                    if novel_response.status_code == 200:
                        print("✅ 小说页面也可以访问！")
                        
                        # 测试章节页面
                        print("\n4. 测试访问章节页面...")
                        # 从小说页面提取一个章节链接
                        from bs4 import BeautifulSoup
                        novel_soup = BeautifulSoup(novel_response.text, 'html.parser')
                        chapter_links = novel_soup.find_all('a', href=lambda href: href and 'chapterNumber=' in href)
                        
                        if chapter_links:
                            chapter_url = chapter_links[0].get('href')
                            if not chapter_url.startswith('http'):
                                chapter_url = 'https://czbooks.net' + chapter_url
                            
                            print(f"测试章节: {chapter_url}")
                            chapter_response = session.get(chapter_url, cookies=cookie_dict, timeout=10)
                            print(f"章节页面状态码: {chapter_response.status_code}")
                            
                            if chapter_response.status_code == 200:
                                print("✅ 章节页面也可以访问！")
                                print("🎉 结论: 可以使用cookie进行直接HTTP请求！")
                                return True, cookie_dict
                            else:
                                print("❌ 章节页面访问失败")
                        else:
                            print("❌ 未找到章节链接")
                    else:
                        print("❌ 小说页面访问失败")
                else:
                    print("❌ 使用cookie的请求也失败")
                    
            else:
                print("❌ Selenium访问失败")
                
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        
    return False, None


def test_login_requirement():
    """检查是否需要登录"""
    print("\n" + "=" * 60)
    print("检查是否需要登录才能访问内容")
    print("=" * 60)
    
    try:
        with SeleniumCZBooksSpider(headless=False, delay=3) as spider:
            print("1. 访问首页，查看是否有登录提示...")
            soup = spider.get_page("https://czbooks.net")
            
            if soup:
                # 查找登录相关元素
                login_indicators = [
                    '登入', '登录', 'login', 'Login', '會員', '会员', 
                    '註冊', '注册', 'register', 'Register'
                ]
                
                found_login = False
                for indicator in login_indicators:
                    if soup.find(text=lambda text: text and indicator in text):
                        print(f"找到登录相关文本: {indicator}")
                        found_login = True
                
                login_links = soup.find_all('a', href=lambda href: href and ('login' in href.lower() or 'signin' in href.lower()))
                if login_links:
                    print(f"找到 {len(login_links)} 个登录链接")
                    found_login = True
                
                if found_login:
                    print("⚠️ 网站可能需要登录才能访问完整内容")
                else:
                    print("✅ 网站似乎不需要登录")
                
                # 检查是否有受限内容提示
                restricted_indicators = ['需要登录', '会员专享', '付费内容', 'VIP']
                for indicator in restricted_indicators:
                    if soup.find(text=lambda text: text and indicator in text):
                        print(f"⚠️ 发现受限内容提示: {indicator}")
                
                return not found_login
                
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        
    return False


def main():
    """主函数"""
    print("CZBooks网站访问权限检查")
    print("=" * 60)
    
    # 测试1: 直接HTTP请求
    direct_success = test_direct_requests()
    
    # 测试2: 使用Selenium获取cookie
    cookie_success, cookies = test_with_selenium_cookies()
    
    # 测试3: 检查登录需求
    no_login_needed = test_login_requirement()
    
    print("\n" + "=" * 60)
    print("📋 测试总结")
    print("=" * 60)
    
    if direct_success:
        print("✅ 可以直接使用HTTP请求，无需Selenium")
        print("💡 建议: 使用标准的requests库即可")
    elif cookie_success:
        print("✅ 需要先用Selenium获取cookie，然后可以用HTTP请求")
        print("💡 建议: 混合使用Selenium+requests")
    else:
        print("❌ 必须使用Selenium进行所有请求")
        print("💡 建议: 只使用Selenium版本")
    
    if no_login_needed:
        print("✅ 无需登录即可访问内容")
    else:
        print("⚠️ 可能需要登录才能访问完整内容")
    
    print("\n🔧 推荐方案:")
    if direct_success:
        print("- 使用 czbooks_spider.py (标准HTTP爬虫)")
    elif cookie_success:
        print("- 先用Selenium获取cookie，再用requests请求")
    else:
        print("- 使用 selenium_spider.py (Selenium爬虫)")


if __name__ == "__main__":
    main()
