#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复SSL问题并测试Cloudflare绕过方法
"""

import ssl
import urllib3
from urllib3.exceptions import InsecureRequestWarning

# 禁用SSL警告
urllib3.disable_warnings(InsecureRequestWarning)


def test_cloudscraper_with_ssl_fix():
    """测试修复SSL问题的cloudscraper"""
    print("=" * 60)
    print("测试 cloudscraper (修复SSL问题)")
    print("=" * 60)
    
    try:
        import cloudscraper
        
        # 创建自定义SSL上下文
        ssl_context = ssl.create_default_context()
        ssl_context.check_hostname = False
        ssl_context.verify_mode = ssl.CERT_NONE
        
        # 创建cloudscraper会话
        scraper = cloudscraper.create_scraper(
            browser={
                'browser': 'chrome',
                'platform': 'darwin',
                'desktop': True
            }
        )
        
        # 设置请求头
        scraper.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        print("1. 测试访问首页...")
        
        # 使用verify=False跳过SSL验证
        response = scraper.get('https://czbooks.net', timeout=15, verify=False)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ cloudscraper 成功绕过Cloudflare！")
            print(f"响应长度: {len(response.text)} 字符")
            
            # 检查是否包含小说内容
            if '小說' in response.text or '小说' in response.text:
                print("✅ 包含小说内容")
                
                # 简单解析小说数量
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(response.text, 'html.parser')
                novel_items = soup.find_all('li', class_='novel-item-wrapper')
                print(f"✅ 找到 {len(novel_items)} 本小说")
                
                if novel_items:
                    first_novel = novel_items[0]
                    title_elem = first_novel.find('div', class_='novel-item-title')
                    if title_elem:
                        title_link = title_elem.find('a')
                        if title_link:
                            print(f"第一本: 《{title_link.get_text(strip=True)}》")
                
                return True
            else:
                print("❌ 未找到小说内容")
                return False
        else:
            print(f"❌ 访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_undetected_with_ssl_fix():
    """测试修复SSL问题的undetected-chromedriver"""
    print("\n" + "=" * 60)
    print("测试 undetected-chromedriver (修复SSL问题)")
    print("=" * 60)
    
    try:
        import undetected_chromedriver as uc
        
        # 配置Chrome选项
        options = uc.ChromeOptions()
        options.add_argument('--headless')
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-web-security')
        options.add_argument('--allow-running-insecure-content')
        options.add_argument('--ignore-certificate-errors')
        options.add_argument('--ignore-ssl-errors')
        options.add_argument('--ignore-certificate-errors-spki-list')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        print("正在启动反检测浏览器...")
        driver = uc.Chrome(options=options, version_main=None)
        
        try:
            # 执行反检测脚本
            driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
            
            print("1. 测试访问首页...")
            driver.get("https://czbooks.net")
            
            # 等待页面加载
            import time
            time.sleep(5)
            
            page_source = driver.page_source
            
            # 检查是否成功
            if '小說' in page_source or '小说' in page_source:
                print("✅ undetected-chromedriver 成功绕过Cloudflare！")
                
                from bs4 import BeautifulSoup
                soup = BeautifulSoup(page_source, 'html.parser')
                novel_items = soup.find_all('li', class_='novel-item-wrapper')
                print(f"✅ 找到 {len(novel_items)} 本小说")
                
                if novel_items:
                    first_novel = novel_items[0]
                    title_elem = first_novel.find('div', class_='novel-item-title')
                    if title_elem:
                        title_link = title_elem.find('a')
                        if title_link:
                            print(f"第一本: 《{title_link.get_text(strip=True)}》")
                
                return True
            else:
                print("❌ 未找到小说内容")
                return False
                
        finally:
            driver.quit()
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def test_requests_with_ssl_fix():
    """测试修复SSL问题的普通requests"""
    print("\n" + "=" * 60)
    print("测试 普通requests (修复SSL问题)")
    print("=" * 60)
    
    try:
        import requests
        
        session = requests.Session()
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
        })
        
        print("1. 测试访问首页...")
        
        # 使用verify=False跳过SSL验证
        response = session.get('https://czbooks.net', timeout=10, verify=False)
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✅ 普通requests 意外成功！")
            print(f"响应长度: {len(response.text)} 字符")
            return True
        else:
            print(f"❌ 访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("Cloudflare绕过方法测试 (修复SSL问题)")
    print("=" * 60)
    
    results = []
    
    # 测试1: cloudscraper (修复SSL)
    success1 = test_cloudscraper_with_ssl_fix()
    results.append(("cloudscraper (SSL修复)", success1))
    
    # 测试2: undetected-chromedriver (修复SSL)
    success2 = test_undetected_with_ssl_fix()
    results.append(("undetected-chromedriver (SSL修复)", success2))
    
    # 测试3: 普通requests (修复SSL)
    success3 = test_requests_with_ssl_fix()
    results.append(("普通requests (SSL修复)", success3))
    
    # 总结结果
    print("\n" + "=" * 60)
    print("📋 测试结果总结")
    print("=" * 60)
    
    successful_methods = []
    
    for method, success in results:
        if success:
            successful_methods.append(method)
            print(f"✅ {method} - 成功")
        else:
            print(f"❌ {method} - 失败")
    
    print(f"\n📊 成功率: {len(successful_methods)}/{len(results)}")
    
    if successful_methods:
        print(f"\n🎯 推荐使用: {successful_methods[0]}")
        
        print("\n💡 关键发现:")
        if "cloudscraper" in successful_methods[0]:
            print("- cloudscraper 可以成功绕过Cloudflare")
            print("- 比Selenium更轻量级")
            print("- 速度更快，资源占用更少")
        elif "undetected-chromedriver" in successful_methods[0]:
            print("- undetected-chromedriver 具有最强的反检测能力")
            print("- 适合处理复杂的验证")
        elif "requests" in successful_methods[0]:
            print("- 普通requests 意外可用")
            print("- 最简单的方案")
    else:
        print("\n❌ 所有方法都失败")
        print("建议继续使用普通Selenium")


if __name__ == "__main__":
    main()
